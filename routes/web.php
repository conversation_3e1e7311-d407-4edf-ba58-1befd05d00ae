<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\QuestionController;
use App\Http\Controllers\QuestionControllerSimple;

// 主页面
Route::get('/', [QuestionControllerSimple::class, 'index'])->name('question.index');

// API路由 - 使用简化版本
Route::post('/api/upload-and-search', [QuestionControllerSimple::class, 'uploadAndSearch'])->name('api.upload-search');
Route::post('/api/search-by-url', [QuestionControllerSimple::class, 'searchByUrl'])->name('api.search-url');

// 调试路由
Route::get('/debug-upload', function() {
    return view('debug-upload');
});

// 日志查看API（仅开发环境）
Route::get('/api/get-logs', function() {
    $logFile = storage_path('logs/laravel.log');
    if (file_exists($logFile)) {
        $logs = file_get_contents($logFile);
        return response($logs)->header('Content-Type', 'text/plain');
    }
    return response('暂无日志文件')->header('Content-Type', 'text/plain');
});

Route::post('/api/clear-logs', function() {
    $logFile = storage_path('logs/laravel.log');
    if (file_exists($logFile)) {
        file_put_contents($logFile, '');
    }
    return response('日志已清空');
});

// 测试路由（无CSRF保护）
Route::post('/test/upload-and-search', [QuestionControllerSimple::class, 'uploadAndSearch'])->withoutMiddleware(['web']);
Route::post('/test/search-by-url', [QuestionControllerSimple::class, 'searchByUrl'])->withoutMiddleware(['web']);
