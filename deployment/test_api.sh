#!/bin/bash

echo "🧪 测试搜题API调用"
echo "=================="

PROJECT_DIR="/Users/<USER>/Herd/paizhao_test"
cd $PROJECT_DIR

echo "1. 清空日志文件..."
> storage/logs/laravel.log

echo "2. 创建测试图片..."
# 创建一个1x1像素的PNG图片
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > /tmp/test_image.png

echo "3. 测试图片上传API..."
echo "发送POST请求到 /api/upload-and-search"

# 获取CSRF token
CSRF_TOKEN=$(curl -s http://localhost:8000 | grep 'csrf-token' | sed 's/.*content="\([^"]*\)".*/\1/')
echo "CSRF Token: $CSRF_TOKEN"

# 发送文件上传请求
echo ""
echo "📤 开始上传测试图片..."
RESPONSE=$(curl -s -X POST \
  -H "X-CSRF-TOKEN: $CSRF_TOKEN" \
  -F "image=@/tmp/test_image.png" \
  http://localhost:8000/api/upload-and-search)

echo ""
echo "📥 API响应结果:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"

echo ""
echo "4. 查看详细日志..."
echo "==================="
if [ -f "storage/logs/laravel.log" ]; then
    cat storage/logs/laravel.log
else
    echo "❌ 没有生成日志文件"
fi

echo ""
echo "5. 测试URL搜题API..."
echo "==================="
echo "发送POST请求到 /api/search-by-url"

URL_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "X-CSRF-TOKEN: $CSRF_TOKEN" \
  -d '{"image_url":"https://example.com/test.jpg"}' \
  http://localhost:8000/api/search-by-url)

echo ""
echo "📥 URL搜题API响应:"
echo "$URL_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$URL_RESPONSE"

echo ""
echo "6. 查看完整日志..."
echo "==================="
if [ -f "storage/logs/laravel.log" ]; then
    echo "最新的日志内容:"
    tail -20 storage/logs/laravel.log
else
    echo "❌ 没有日志文件"
fi

# 清理测试文件
rm -f /tmp/test_image.png

echo ""
echo "🎉 测试完成！"
echo ""
echo "💡 如需实时监控日志，请运行:"
echo "   bash deployment/watch_logs.sh"
