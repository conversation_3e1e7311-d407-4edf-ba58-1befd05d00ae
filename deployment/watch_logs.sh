#!/bin/bash

echo "📋 实时查看拍照搜题系统日志"
echo "============================="
echo "请在另一个终端窗口中测试上传图片功能"
echo "按 Ctrl+C 停止日志监控"
echo ""

PROJECT_DIR="/Users/<USER>/Herd/paizhao_test"
LOG_FILE="$PROJECT_DIR/storage/logs/laravel.log"

# 检查日志文件是否存在
if [ ! -f "$LOG_FILE" ]; then
    echo "⚠️ 日志文件不存在，创建新的日志文件..."
    touch "$LOG_FILE"
fi

echo "🔍 开始监控日志文件: $LOG_FILE"
echo "---------------------------------------------"

# 实时监控日志文件
tail -f "$LOG_FILE" | while read line; do
    # 高亮显示重要信息
    if [[ $line == *"搜题API"* ]] || [[ $line == *"Mock"* ]] || [[ $line == *"formatted_result"* ]]; then
        echo -e "\033[1;32m$line\033[0m"  # 绿色高亮
    elif [[ $line == *"ERROR"* ]]; then
        echo -e "\033[1;31m$line\033[0m"  # 红色高亮
    elif [[ $line == *"WARNING"* ]]; then
        echo -e "\033[1;33m$line\033[0m"  # 黄色高亮
    else
        echo "$line"
    fi
done
