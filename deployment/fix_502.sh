#!/bin/bash

echo "🔧 502错误自动修复脚本"
echo "=========================="

PROJECT_DIR="/www/wwwroot/paizhao"

# 1. 检查PHP-FPM状态
echo "1. 检查PHP-FPM状态..."
if systemctl is-active --quiet php8.2-fpm; then
    echo "   ✅ PHP-FPM正在运行"
else
    echo "   ❌ PHP-FPM未运行，正在重启..."
    systemctl restart php8.2-fpm
    sleep 3
    if systemctl is-active --quiet php8.2-fpm; then
        echo "   ✅ PHP-FPM重启成功"
    else
        echo "   ❌ PHP-FPM重启失败，请检查配置"
        exit 1
    fi
fi

# 2. 检查项目目录
echo "2. 检查项目目录..."
if [ ! -d "$PROJECT_DIR" ]; then
    echo "   ❌ 项目目录不存在：$PROJECT_DIR"
    exit 1
fi

cd $PROJECT_DIR

# 3. 检查关键文件
echo "3. 检查关键文件..."
if [ ! -f "public/index.php" ]; then
    echo "   ❌ 入口文件不存在：public/index.php"
    exit 1
fi

if [ ! -f ".env" ]; then
    echo "   ❌ 环境配置文件不存在：.env"
    echo "   正在复制配置文件..."
    if [ -f "deployment/.env.bt" ]; then
        cp deployment/.env.bt .env
        echo "   ✅ 环境配置文件已复制"
    else
        cp .env.example .env
        echo "   ✅ 使用默认环境配置"
    fi
fi

# 4. 检查应用密钥
echo "4. 检查应用密钥..."
if ! grep -q "APP_KEY=base64:" .env; then
    echo "   ❌ 应用密钥未设置，正在生成..."
    php artisan key:generate --force
    echo "   ✅ 应用密钥已生成"
fi

# 5. 检查数据库
echo "5. 检查数据库..."
if [ ! -f "database/database.sqlite" ]; then
    echo "   ❌ SQLite数据库不存在，正在创建..."
    touch database/database.sqlite
    chmod 664 database/database.sqlite
    chown www:www database/database.sqlite
    echo "   ✅ SQLite数据库已创建"
fi

# 6. 运行数据库迁移
echo "6. 运行数据库迁移..."
php artisan migrate --force 2>/dev/null
echo "   ✅ 数据库迁移完成"

# 7. 清除缓存
echo "7. 清除应用缓存..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear
echo "   ✅ 缓存已清除"

# 8. 重新生成缓存
echo "8. 重新生成缓存..."
php artisan config:cache
php artisan route:cache
php artisan view:cache
echo "   ✅ 缓存已重新生成"

# 9. 设置文件权限
echo "9. 设置文件权限..."
chown -R www:www $PROJECT_DIR
chmod -R 755 $PROJECT_DIR
chmod -R 775 storage
chmod -R 775 bootstrap/cache
chmod 664 database/database.sqlite
chmod 775 database
echo "   ✅ 文件权限已设置"

# 10. 检查Composer依赖
echo "10. 检查Composer依赖..."
if [ ! -d "vendor" ]; then
    echo "    ❌ vendor目录不存在，正在安装依赖..."
    if [ ! -f "composer.phar" ]; then
        curl -sS https://getcomposer.org/installer | php
    fi
    php composer.phar install --no-dev --optimize-autoloader
    echo "    ✅ Composer依赖已安装"
fi

# 11. 重启服务
echo "11. 重启相关服务..."
systemctl reload nginx
systemctl restart php8.2-fpm
echo "    ✅ 服务已重启"

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 请检查以下内容："
echo "   1. 访问网站是否正常"
echo "   2. 查看错误日志：tail -f /var/log/nginx/error.log"
echo "   3. 查看PHP日志：tail -f /var/log/php8.2-fpm.log"
echo ""
echo "如果问题仍然存在，请检查："
echo "   - Nginx配置是否正确"
echo "   - PHP扩展是否完整"
echo "   - 服务器资源是否充足"
