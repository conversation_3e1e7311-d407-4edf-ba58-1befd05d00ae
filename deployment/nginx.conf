# 开发/测试环境配置 (HTTP)
server {
    listen 80;
    server_name your-domain.com www.your-domain.com localhost;

    root /var/www/paizhao/public;
    index index.php index.html index.htm;

    # 如果有SSL证书，取消注释以下行并注释掉上面的HTTP配置
    # return 301 https://$server_name$request_uri;
}

# 生产环境HTTPS配置 (需要SSL证书时启用)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com www.your-domain.com;
#
#     root /var/www/paizhao/public;
#     index index.php index.html index.htm;
#
#     # SSL证书配置
#     ssl_certificate /etc/ssl/certs/your-domain.com.crt;
#     ssl_certificate_key /etc/ssl/private/your-domain.com.key;
#
#     # SSL安全配置
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
#     ssl_prefer_server_ciphers off;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 文件上传大小限制
    client_max_body_size 20M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;
    
    # 静态文件缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|pdf|txt)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }
    
    # 隐藏敏感文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ^/(\.env|composer\.(json|lock)|package\.json|artisan) {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Laravel应用配置
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
        
        # 安全配置
        fastcgi_param HTTP_PROXY "";
        fastcgi_param HTTPS on;
        fastcgi_param SERVER_PORT 443;
        
        # 性能优化
        fastcgi_buffer_size 128k;
        fastcgi_buffers 4 256k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_read_timeout 300;
    }
    
    # 日志配置
    access_log /var/log/nginx/paizhao_access.log;
    error_log /var/log/nginx/paizhao_error.log;
}

# API限流配置
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;

# API接口特殊配置
location /api/ {
    limit_req zone=api burst=20 nodelay;
    try_files $uri $uri/ /index.php?$query_string;
}
