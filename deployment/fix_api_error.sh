#!/bin/bash

echo "🔧 修复搜题API错误"
echo "=================="

PROJECT_DIR="/Users/<USER>/Herd/paizhao_test"
cd $PROJECT_DIR

echo "1. 检查当前API配置..."
grep "QUESTION_API_URL" .env

echo ""
echo "2. 清除应用缓存..."
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo ""
echo "3. 重新生成缓存..."
php artisan config:cache
php artisan route:cache

echo ""
echo "4. 检查日志文件..."
if [ -f "storage/logs/laravel.log" ]; then
    echo "最近的错误日志："
    tail -5 storage/logs/laravel.log
else
    echo "暂无错误日志"
fi

echo ""
echo "5. 测试模拟API..."
php -r "
require 'vendor/autoload.php';
\$app = require_once 'bootstrap/app.php';
\$mock = new App\Services\MockQuestionSearchService();
\$result = \$mock->searchQuestion('https://example.com/test.jpg');
echo '模拟API测试结果: ' . json_encode(\$result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
"

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 当前配置："
echo "- 系统已自动切换到模拟API模式"
echo "- 可以正常演示搜题功能"
echo "- 如需使用真实API，请更新 .env 中的 QUESTION_API_URL"
echo ""
echo "🌐 访问地址：http://localhost:8000"
