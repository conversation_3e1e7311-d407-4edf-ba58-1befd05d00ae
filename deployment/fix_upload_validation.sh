#!/bin/bash

echo "🔧 修复图片上传验证问题"
echo "========================"

PROJECT_DIR="/www/wwwroot/paizhao"
cd $PROJECT_DIR

echo "1. 检查PHP扩展..."
php -m | grep -E "(fileinfo|gd)" || echo "⚠️ 缺少关键扩展"

echo "2. 部署调试工具..."
cp deployment/debug_upload.php public/
echo "   ✅ 调试工具已部署到 /public/debug_upload.php"

echo "3. 清除应用缓存..."
php artisan config:clear
php artisan route:clear
php artisan view:clear

echo "4. 重新生成缓存..."
php artisan config:cache
php artisan route:cache

echo "5. 设置文件权限..."
chown -R www:www $PROJECT_DIR
chmod -R 755 $PROJECT_DIR
chmod -R 775 storage

echo "6. 重启PHP服务..."
if command -v systemctl &> /dev/null; then
    systemctl restart php8.2-fpm
elif [ -f "/etc/init.d/php-fpm-82" ]; then
    /etc/init.d/php-fpm-82 restart
fi

echo ""
echo "🎉 修复完成！"
echo ""
echo "📋 测试步骤："
echo "1. 访问调试工具: http://your-domain.com/debug_upload.php"
echo "2. 上传一张测试图片查看详细信息"
echo "3. 根据调试结果进行进一步修复"
echo ""
echo "💡 如果问题仍然存在："
echo "- 检查是否安装了fileinfo扩展"
echo "- 检查PHP上传限制设置"
echo "- 查看错误日志获取详细信息"
