#!/bin/bash

echo "🧪 测试真实搜题API"
echo "=================="

PROJECT_DIR="/Users/<USER>/Herd/paizhao_test"
cd $PROJECT_DIR

echo "1. 当前API配置:"
echo "   URL: $(php artisan config:show services.question_api.url)"
echo "   KEY: $(php artisan config:show services.question_api.key)"

echo ""
echo "2. 选择测试模式:"
echo "   [1] 使用模拟API (当前)"
echo "   [2] 使用真实API"
read -p "请选择 (1/2): " choice

if [ "$choice" = "2" ]; then
    echo ""
    echo "🔄 切换到真实API..."
    
    # 备份当前.env
    cp .env .env.backup
    
    # 启用真实API
    sed -i '' 's/^QUESTION_API_URL=$/QUESTION_API_URL=http:\/\/121.196.211.20:8080\/api\/v1\/api\/search/' .env
    
    # 清除缓存
    php artisan config:clear
    
    echo "✅ 已切换到真实API"
    echo "   URL: $(php artisan config:show services.question_api.url)"
else
    echo ""
    echo "📝 继续使用模拟API"
fi

echo ""
echo "3. 清空日志..."
> storage/logs/laravel.log

echo ""
echo "4. 测试API调用..."

# 创建测试图片
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > /tmp/test_image.png

# 获取CSRF token
CSRF_TOKEN=$(curl -s http://localhost:8000 | grep 'csrf-token' | sed 's/.*content="\([^"]*\)".*/\1/')

echo "📤 发送上传请求..."
RESPONSE=$(curl -s -X POST \
  -H "X-CSRF-TOKEN: $CSRF_TOKEN" \
  -F "image=@/tmp/test_image.png" \
  http://localhost:8000/api/upload-and-search)

echo ""
echo "📥 API响应:"
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"

echo ""
echo "📋 详细日志:"
echo "============"
cat storage/logs/laravel.log

echo ""
echo "🧪 测试URL搜题..."
URL_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -H "X-CSRF-TOKEN: $CSRF_TOKEN" \
  -d '{"image_url":"https://example.com/test.jpg"}' \
  http://localhost:8000/api/search-by-url)

echo ""
echo "📥 URL搜题响应:"
echo "$URL_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$URL_RESPONSE"

echo ""
echo "📋 完整日志:"
echo "============"
tail -20 storage/logs/laravel.log

# 清理
rm -f /tmp/test_image.png

if [ "$choice" = "2" ]; then
    echo ""
    echo "🔄 是否恢复到模拟API? (y/n)"
    read -p "请选择: " restore
    
    if [ "$restore" = "y" ]; then
        mv .env.backup .env
        php artisan config:clear
        echo "✅ 已恢复到模拟API"
    else
        rm -f .env.backup
        echo "📝 保持真实API配置"
    fi
fi

echo ""
echo "🎉 测试完成！"
