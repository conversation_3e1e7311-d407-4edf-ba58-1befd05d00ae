<?php
/**
 * 检测fileinfo扩展状态的脚本
 * 使用方法：php check_fileinfo.php
 */

echo "🔍 检测PHP环境和扩展状态...\n\n";

// 1. 检查PHP版本
echo "📋 PHP版本信息：\n";
echo "   版本：" . PHP_VERSION . "\n";
echo "   SAPI：" . php_sapi_name() . "\n\n";

// 2. 检查fileinfo扩展
echo "🔧 fileinfo扩展检测：\n";
if (extension_loaded('fileinfo')) {
    echo "   ✅ fileinfo扩展已安装\n";
    
    // 测试fileinfo功能
    $testFile = __FILE__;
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    if ($finfo) {
        $mimeType = finfo_file($finfo, $testFile);
        echo "   ✅ fileinfo功能正常，测试文件MIME类型：$mimeType\n";
        finfo_close($finfo);
    } else {
        echo "   ⚠️ fileinfo扩展已安装但无法正常工作\n";
    }
} else {
    echo "   ❌ fileinfo扩展未安装\n";
    echo "   🚨 这将导致文件上传验证失败！\n";
}

// 3. 检查其他相关扩展
echo "\n🔧 其他相关扩展检测：\n";

$requiredExtensions = [
    'gd' => 'GD图像处理库',
    'curl' => 'cURL网络请求',
    'mbstring' => '多字节字符串处理',
    'xml' => 'XML处理',
    'zip' => 'ZIP压缩',
    'sqlite3' => 'SQLite数据库',
    'opcache' => 'OPcache性能优化'
];

foreach ($requiredExtensions as $ext => $desc) {
    if (extension_loaded($ext)) {
        echo "   ✅ $ext ($desc)\n";
    } else {
        echo "   ❌ $ext ($desc) - 未安装\n";
    }
}

// 4. 检查文件上传配置
echo "\n📁 文件上传配置：\n";
echo "   upload_max_filesize: " . ini_get('upload_max_filesize') . "\n";
echo "   post_max_size: " . ini_get('post_max_size') . "\n";
echo "   max_file_uploads: " . ini_get('max_file_uploads') . "\n";
echo "   file_uploads: " . (ini_get('file_uploads') ? '启用' : '禁用') . "\n";

// 5. 测试图片验证功能
echo "\n🖼️ 图片验证功能测试：\n";

// 创建一个测试图片数据
$testImageData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
$testImagePath = sys_get_temp_dir() . '/test_image.png';
file_put_contents($testImagePath, $testImageData);

if (extension_loaded('fileinfo')) {
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mimeType = finfo_file($finfo, $testImagePath);
    echo "   ✅ fileinfo检测结果：$mimeType\n";
    finfo_close($finfo);
} else {
    echo "   ❌ 无法使用fileinfo检测MIME类型\n";
}

if (extension_loaded('gd')) {
    $imageInfo = getimagesize($testImagePath);
    if ($imageInfo) {
        echo "   ✅ GD库检测结果：{$imageInfo[0]}x{$imageInfo[1]}, 类型：{$imageInfo[2]}\n";
    } else {
        echo "   ❌ GD库无法识别图片\n";
    }
} else {
    echo "   ❌ GD库未安装，无法验证图片\n";
}

// 清理测试文件
unlink($testImagePath);

// 6. 给出建议
echo "\n💡 建议和解决方案：\n";

if (!extension_loaded('fileinfo')) {
    echo "   🔧 安装fileinfo扩展：\n";
    echo "      - 宝塔面板：软件商店 → PHP设置 → 安装扩展 → fileinfo\n";
    echo "      - Ubuntu/Debian: sudo apt-get install php-fileinfo\n";
    echo "      - CentOS/RHEL: sudo yum install php-fileinfo\n\n";
    
    echo "   ⚠️ 如果无法安装fileinfo，系统已提供备用验证方案\n";
    echo "      - 使用文件头签名验证\n";
    echo "      - 使用GD库验证\n";
    echo "      - 恶意内容扫描\n\n";
}

if (!extension_loaded('gd')) {
    echo "   🔧 建议安装GD扩展以增强图片验证能力\n\n";
}

$uploadMaxSize = ini_get('upload_max_filesize');
if (intval($uploadMaxSize) < 20) {
    echo "   🔧 建议增加上传文件大小限制到20M\n";
    echo "      - 修改 upload_max_filesize = 20M\n";
    echo "      - 修改 post_max_size = 25M\n\n";
}

echo "✅ 检测完成！\n";
?>
