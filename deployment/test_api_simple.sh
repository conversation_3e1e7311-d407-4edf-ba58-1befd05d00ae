#!/bin/bash

echo "🧪 简化API测试（无CSRF）"
echo "========================"

PROJECT_DIR="/Users/<USER>/Herd/paizhao_test"
cd $PROJECT_DIR

echo "1. 清空日志..."
> storage/logs/laravel.log

echo "2. 清除缓存..."
php artisan route:clear

echo "3. 创建测试图片..."
echo "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==" | base64 -d > /tmp/test_image.png

echo "4. 测试文件上传API（无CSRF）..."
echo "发送POST请求到 /test/upload-and-search"

RESPONSE=$(curl -s -X POST \
  -F "image=@/tmp/test_image.png" \
  http://localhost:8000/test/upload-and-search)

echo ""
echo "📥 文件上传API响应:"
echo "==================="
echo "$RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$RESPONSE"

echo ""
echo "5. 测试URL搜题API（无CSRF）..."
echo "发送POST请求到 /test/search-by-url"

URL_RESPONSE=$(curl -s -X POST \
  -H "Content-Type: application/json" \
  -d '{"image_url":"https://example.com/test.jpg"}' \
  http://localhost:8000/test/search-by-url)

echo ""
echo "📥 URL搜题API响应:"
echo "=================="
echo "$URL_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$URL_RESPONSE"

echo ""
echo "6. 查看详细日志:"
echo "================"
if [ -f "storage/logs/laravel.log" ]; then
    cat storage/logs/laravel.log
else
    echo "❌ 没有生成日志文件"
fi

# 清理测试文件
rm -f /tmp/test_image.png

echo ""
echo "🎉 测试完成！"
echo ""
echo "📋 总结:"
echo "- 如果看到JSON格式的响应，说明API工作正常"
echo "- 如果看到HTML错误页面，说明还有其他问题"
echo "- 查看日志了解详细的数据处理过程"
