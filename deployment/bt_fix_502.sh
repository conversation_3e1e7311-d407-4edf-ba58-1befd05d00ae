#!/bin/bash

echo "🔧 宝塔面板502错误修复脚本"
echo "============================="

PROJECT_DIR="/www/wwwroot/paizhao"

# 检查是否为宝塔环境
if [ ! -f "/www/server/panel/BT-Panel" ]; then
    echo "❌ 未检测到宝塔面板环境"
    echo "请使用通用修复脚本：bash deployment/fix_502.sh"
    exit 1
fi

echo "✅ 检测到宝塔面板环境"

# 1. 重启宝塔相关服务
echo "1. 重启宝塔服务..."
/etc/init.d/nginx restart
/etc/init.d/php-fpm-82 restart
echo "   ✅ 服务重启完成"

# 2. 检查网站配置
echo "2. 检查网站配置..."
cd $PROJECT_DIR

# 检查网站运行目录是否正确
echo "   检查运行目录设置..."
echo "   确保在宝塔面板中网站运行目录设置为：/public"

# 3. 检查PHP版本和扩展
echo "3. 检查PHP配置..."
PHP_VERSION=$(php -v | head -n1 | cut -d' ' -f2 | cut -d'.' -f1,2)
echo "   当前PHP版本：$PHP_VERSION"

# 检查必需扩展
REQUIRED_EXTENSIONS=("fileinfo" "curl" "gd" "mbstring" "xml" "zip" "sqlite3")
for ext in "${REQUIRED_EXTENSIONS[@]}"; do
    if php -m | grep -q "$ext"; then
        echo "   ✅ $ext 扩展已安装"
    else
        echo "   ❌ $ext 扩展未安装"
        echo "      请在宝塔面板 → 软件商店 → PHP$PHP_VERSION → 设置 → 安装扩展中安装"
    fi
done

# 4. 检查文件权限（宝塔专用）
echo "4. 设置宝塔文件权限..."
chown -R www:www $PROJECT_DIR
find $PROJECT_DIR -type d -exec chmod 755 {} \;
find $PROJECT_DIR -type f -exec chmod 644 {} \;
chmod -R 775 storage
chmod -R 775 bootstrap/cache
chmod 664 database/database.sqlite
echo "   ✅ 权限设置完成"

# 5. 检查.env配置
echo "5. 检查环境配置..."
if [ ! -f ".env" ]; then
    if [ -f "deployment/.env.bt" ]; then
        cp deployment/.env.bt .env
        echo "   ✅ 宝塔环境配置已复制"
    else
        cp .env.example .env
        echo "   ✅ 默认环境配置已复制"
    fi
fi

# 更新APP_URL为当前域名
if command -v bt &> /dev/null; then
    DOMAIN=$(bt domain | head -n1 2>/dev/null || echo "localhost")
    sed -i "s|APP_URL=.*|APP_URL=http://$DOMAIN|g" .env
    echo "   ✅ 域名配置已更新：$DOMAIN"
fi

# 6. Laravel应用初始化
echo "6. Laravel应用初始化..."
php artisan key:generate --force
php artisan migrate --force 2>/dev/null

# 清除缓存
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

# 重新生成缓存
php artisan config:cache
php artisan route:cache
php artisan view:cache

echo "   ✅ Laravel应用初始化完成"

# 7. 检查伪静态规则
echo "7. 检查伪静态规则..."
NGINX_CONF="/www/server/panel/vhost/nginx/$DOMAIN.conf"
if [ -f "$NGINX_CONF" ]; then
    if grep -q "try_files.*index.php" "$NGINX_CONF"; then
        echo "   ✅ 伪静态规则已配置"
    else
        echo "   ⚠️ 请在宝塔面板中设置伪静态规则为Laravel"
    fi
else
    echo "   ⚠️ 请检查网站配置文件"
fi

# 8. 检查PHP配置
echo "8. 检查PHP配置..."
UPLOAD_MAX=$(php -r "echo ini_get('upload_max_filesize');")
POST_MAX=$(php -r "echo ini_get('post_max_size');")
MEMORY_LIMIT=$(php -r "echo ini_get('memory_limit');")

echo "   upload_max_filesize: $UPLOAD_MAX"
echo "   post_max_size: $POST_MAX"
echo "   memory_limit: $MEMORY_LIMIT"

if [[ ${UPLOAD_MAX%M} -lt 20 ]]; then
    echo "   ⚠️ 建议在宝塔面板中将上传限制调整为20M"
fi

# 9. 测试应用
echo "9. 测试应用..."
if php artisan --version >/dev/null 2>&1; then
    echo "   ✅ Laravel应用正常"
else
    echo "   ❌ Laravel应用异常"
fi

# 10. 最终重启
echo "10. 最终重启服务..."
/etc/init.d/nginx reload
/etc/init.d/php-fpm-82 restart

echo ""
echo "🎉 宝塔环境修复完成！"
echo ""
echo "📋 请在宝塔面板中检查："
echo "   1. 网站 → 设置 → 网站目录 → 运行目录设置为 /public"
echo "   2. 网站 → 设置 → 伪静态 → 选择 Laravel"
echo "   3. 软件商店 → PHP → 设置 → 上传限制调整为 20M"
echo "   4. 软件商店 → PHP → 设置 → 安装必需扩展"
echo ""
echo "📱 访问测试：http://your-domain.com"
echo ""
echo "如果仍有问题，请查看："
echo "   - 宝塔面板 → 网站 → 日志"
echo "   - 宝塔面板 → 软件商店 → PHP → 日志"
