<?php
/**
 * 图片上传调试脚本
 * 放在public目录下，通过浏览器访问进行调试
 */
?>
<!DOCTYPE html>
<html>
<head>
    <title>图片上传调试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .result { background: #f5f5f5; padding: 15px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffe6e6; border-left: 4px solid #ff0000; }
        .success { background: #e6ffe6; border-left: 4px solid #00ff00; }
        .info { background: #e6f3ff; border-left: 4px solid #0066cc; }
    </style>
</head>
<body>
    <h1>🔍 图片上传调试工具</h1>
    
    <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_image'])): ?>
        <div class="result">
            <h2>📋 调试结果</h2>
            
            <?php
            $file = $_FILES['test_image'];
            
            echo "<h3>📁 文件基本信息</h3>";
            echo "<ul>";
            echo "<li>原始文件名: " . htmlspecialchars($file['name']) . "</li>";
            echo "<li>临时文件路径: " . htmlspecialchars($file['tmp_name']) . "</li>";
            echo "<li>文件大小: " . number_format($file['size']) . " 字节 (" . round($file['size']/1024/1024, 2) . " MB)</li>";
            echo "<li>上传错误码: " . $file['error'] . "</li>";
            echo "<li>MIME类型(客户端): " . htmlspecialchars($file['type']) . "</li>";
            echo "</ul>";
            
            if ($file['error'] !== UPLOAD_ERR_OK) {
                echo "<div class='error'>";
                echo "<h3>❌ 上传错误</h3>";
                $errors = [
                    UPLOAD_ERR_INI_SIZE => '文件大小超过php.ini中upload_max_filesize的值',
                    UPLOAD_ERR_FORM_SIZE => '文件大小超过表单中MAX_FILE_SIZE的值',
                    UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
                    UPLOAD_ERR_NO_FILE => '没有文件被上传',
                    UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
                    UPLOAD_ERR_CANT_WRITE => '文件写入失败',
                    UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止'
                ];
                echo "<p>" . ($errors[$file['error']] ?? "未知错误: " . $file['error']) . "</p>";
                echo "</div>";
            } else {
                echo "<div class='success'>";
                echo "<h3>✅ 文件上传成功</h3>";
                echo "</div>";
                
                // 检查文件扩展名
                echo "<h3>🔧 扩展名检查</h3>";
                $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
                $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
                if (in_array($extension, $allowedExtensions)) {
                    echo "<div class='success'>✅ 扩展名有效: $extension</div>";
                } else {
                    echo "<div class='error'>❌ 扩展名无效: $extension</div>";
                }
                
                // 检查fileinfo扩展
                echo "<h3>🔧 MIME类型检查</h3>";
                if (extension_loaded('fileinfo')) {
                    $finfo = finfo_open(FILEINFO_MIME_TYPE);
                    $mimeType = finfo_file($finfo, $file['tmp_name']);
                    finfo_close($finfo);
                    echo "<div class='success'>✅ fileinfo扩展可用</div>";
                    echo "<p>检测到的MIME类型: <strong>$mimeType</strong></p>";
                    
                    $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
                    if (in_array($mimeType, $allowedMimes)) {
                        echo "<div class='success'>✅ MIME类型有效</div>";
                    } else {
                        echo "<div class='error'>❌ MIME类型无效</div>";
                    }
                } else {
                    echo "<div class='error'>❌ fileinfo扩展未安装</div>";
                }
                
                // 检查GD库
                echo "<h3>🔧 GD库检查</h3>";
                if (extension_loaded('gd')) {
                    $imageInfo = getimagesize($file['tmp_name']);
                    if ($imageInfo) {
                        echo "<div class='success'>✅ GD库验证成功</div>";
                        echo "<ul>";
                        echo "<li>图片尺寸: {$imageInfo[0]} x {$imageInfo[1]}</li>";
                        echo "<li>图片类型: " . image_type_to_mime_type($imageInfo[2]) . "</li>";
                        echo "<li>类型常量: {$imageInfo[2]}</li>";
                        echo "</ul>";
                    } else {
                        echo "<div class='error'>❌ GD库无法识别此文件为图片</div>";
                    }
                } else {
                    echo "<div class='error'>❌ GD扩展未安装</div>";
                }
                
                // 检查文件头
                echo "<h3>🔧 文件头检查</h3>";
                $handle = fopen($file['tmp_name'], 'rb');
                $header = fread($handle, 12);
                fclose($handle);
                
                echo "<p>文件头(十六进制): " . bin2hex($header) . "</p>";
                echo "<p>文件头(ASCII): " . htmlspecialchars($header) . "</p>";
                
                // 检查常见图片格式签名
                $signatures = [
                    'JPEG' => ["\xFF\xD8\xFF"],
                    'PNG' => ["\x89\x50\x4E\x47"],
                    'GIF' => ["GIF87a", "GIF89a"],
                    'WebP' => ["RIFF"]
                ];
                
                $detected = false;
                foreach ($signatures as $format => $sigs) {
                    foreach ($sigs as $sig) {
                        if (substr($header, 0, strlen($sig)) === $sig) {
                            if ($format === 'WebP' && substr($header, 8, 4) !== 'WEBP') {
                                continue;
                            }
                            echo "<div class='success'>✅ 检测到 $format 格式</div>";
                            $detected = true;
                            break 2;
                        }
                    }
                }
                
                if (!$detected) {
                    echo "<div class='error'>❌ 未识别的文件格式</div>";
                }
            }
            ?>
        </div>
    <?php endif; ?>
    
    <form method="post" enctype="multipart/form-data">
        <h2>📤 上传测试图片</h2>
        <p>选择一张图片进行详细的格式检查：</p>
        <input type="file" name="test_image" accept="image/*" required>
        <br><br>
        <button type="submit">🔍 开始调试</button>
    </form>
    
    <div class="result info">
        <h3>📋 系统信息</h3>
        <ul>
            <li>PHP版本: <?php echo PHP_VERSION; ?></li>
            <li>fileinfo扩展: <?php echo extension_loaded('fileinfo') ? '✅ 已安装' : '❌ 未安装'; ?></li>
            <li>GD扩展: <?php echo extension_loaded('gd') ? '✅ 已安装' : '❌ 未安装'; ?></li>
            <li>上传限制: <?php echo ini_get('upload_max_filesize'); ?></li>
            <li>POST限制: <?php echo ini_get('post_max_size'); ?></li>
            <li>内存限制: <?php echo ini_get('memory_limit'); ?></li>
        </ul>
    </div>
</body>
</html>
