<?php
// 简单的PHP测试文件
echo "🎉 PHP正常工作！\n";
echo "PHP版本：" . PHP_VERSION . "\n";
echo "当前时间：" . date('Y-m-d H:i:s') . "\n";

// 检查扩展
$extensions = ['fileinfo', 'curl', 'gd', 'mbstring', 'xml', 'zip', 'sqlite3'];
echo "\n扩展检查：\n";
foreach ($extensions as $ext) {
    echo "  $ext: " . (extension_loaded($ext) ? '✅' : '❌') . "\n";
}

// 检查文件权限
echo "\n文件权限检查：\n";
$paths = [
    '../storage' => is_writable('../storage'),
    '../bootstrap/cache' => is_writable('../bootstrap/cache'),
    '../database/database.sqlite' => file_exists('../database/database.sqlite') && is_writable('../database/database.sqlite')
];

foreach ($paths as $path => $writable) {
    echo "  $path: " . ($writable ? '✅' : '❌') . "\n";
}

phpinfo();
?>
