#!/bin/bash

# 宝塔面板一键部署脚本
# 使用方法：bash bt_deploy.sh

echo "🚀 开始部署拍照搜题系统到宝塔面板..."

# 检查是否在正确目录
if [ ! -f "artisan" ]; then
    echo "❌ 错误：请在Laravel项目根目录执行此脚本"
    exit 1
fi

# 设置变量
PROJECT_DIR="/www/wwwroot/paizhao"
WEB_USER="www"

echo "📁 当前目录：$(pwd)"
echo "🎯 目标目录：$PROJECT_DIR"

# 1. 安装Composer依赖
echo "📦 安装Composer依赖..."
if [ ! -f "composer.phar" ]; then
    curl -sS https://getcomposer.org/installer | php
fi
php composer.phar install --no-dev --optimize-autoloader

# 2. 复制环境配置
echo "⚙️ 配置环境文件..."
if [ -f "deployment/.env.bt" ]; then
    cp deployment/.env.bt .env
    echo "✅ 环境配置文件已复制"
else
    echo "⚠️ 警告：未找到宝塔环境配置文件，使用默认配置"
    cp .env.example .env
fi

# 3. 生成应用密钥
echo "🔑 生成应用密钥..."
php artisan key:generate --force

# 4. 创建SQLite数据库
echo "🗄️ 创建数据库..."
touch database/database.sqlite

# 5. 运行数据库迁移
echo "📊 运行数据库迁移..."
php artisan migrate --force

# 6. 清除并缓存配置
echo "🚀 优化应用性能..."
php artisan config:clear
php artisan route:clear
php artisan view:clear
php artisan cache:clear

php artisan config:cache
php artisan route:cache
php artisan view:cache

# 7. 设置文件权限
echo "🔒 设置文件权限..."
chown -R $WEB_USER:$WEB_USER $PROJECT_DIR
chmod -R 755 $PROJECT_DIR
chmod -R 775 storage
chmod -R 775 bootstrap/cache
chmod 664 database/database.sqlite
chmod 775 database

# 8. 创建必要目录
echo "📁 创建必要目录..."
mkdir -p storage/logs
mkdir -p storage/framework/cache
mkdir -p storage/framework/sessions
mkdir -p storage/framework/views

# 9. 设置存储目录权限
chmod -R 775 storage
chown -R $WEB_USER:$WEB_USER storage

echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 部署信息："
echo "   项目目录：$PROJECT_DIR"
echo "   网站根目录：$PROJECT_DIR/public"
echo "   数据库：SQLite (database/database.sqlite)"
echo "   缓存方式：文件缓存"
echo ""
echo "🔧 后续配置："
echo "1. 在宝塔面板中将网站运行目录设置为 /public"
echo "2. 配置伪静态规则为 Laravel"
echo "3. 设置PHP上传限制为 20M"
echo "4. 检查域名和SSL证书配置"
echo ""
echo "🌐 访问地址：http://your-domain.com"
echo ""
echo "✅ 系统已就绪，可以开始使用！"
