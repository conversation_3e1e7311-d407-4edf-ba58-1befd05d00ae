# 宝塔部署专用环境配置
APP_NAME="拍照搜题系统"
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_TIMEZONE=Asia/Shanghai
APP_URL=http://your-domain.com

APP_LOCALE=zh
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=zh_CN

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=file

BCRYPT_ROUNDS=12

# 日志配置
LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# 数据库配置 (使用SQLite)
DB_CONNECTION=sqlite
# 其他数据库配置已注释，无需配置

# 会话和缓存配置 (使用文件存储)
SESSION_DRIVER=file
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null
SESSION_SECURE_COOKIE=false
SESSION_SAME_SITE=lax

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync

CACHE_STORE=file
CACHE_PREFIX=paizhao_

# 邮件配置 (系统不需要邮件功能)
MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# 阿里云OSS配置
OSS_ACCESS_KEY_ID=LTAI5tQeXNXM8aBxAK3bH8NS
OSS_ACCESS_KEY_SECRET=******************************
OSS_BUCKET=lnterstellar
OSS_ENDPOINT=oss-cn-zhangjiakou.aliyuncs.com
OSS_DOMAIN=lnterstellar.oss-cn-zhangjiakou.aliyuncs.com

# 拍照搜题API配置
QUESTION_API_KEY=IwHA484058wQ1Jml8LrRECZbGRYW9Nb6
QUESTION_API_SECRET=G8Z5G4ZT7BZ5PRUnZIpsZeWqx9XvxJLHMEN15rmX3Mhp0E2B6exmysaz9FwP0gbO
QUESTION_API_URL=https://your-api-domain.com/api/v1/api/search

VITE_APP_NAME="${APP_NAME}"
