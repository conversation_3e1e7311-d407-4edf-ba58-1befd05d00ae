// 获取CSRF token
const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

// DOM元素
const uploadArea = document.getElementById('uploadArea');
const imageInput = document.getElementById('imageInput');
const imageUrl = document.getElementById('imageUrl');
const searchByUrlBtn = document.getElementById('searchByUrl');
const loading = document.getElementById('loading');
const errorAlert = document.getElementById('errorAlert');
const errorMessage = document.getElementById('errorMessage');
const resultContainer = document.getElementById('resultContainer');

// 初始化事件监听器
document.addEventListener('DOMContentLoaded', function() {
    // 文件上传事件
    imageInput.addEventListener('change', handleFileUpload);
    
    // URL搜索事件
    searchByUrlBtn.addEventListener('click', handleUrlSearch);
    imageUrl.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            handleUrlSearch();
        }
    });

    // 拖拽上传事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleDrop);
    uploadArea.addEventListener('click', function() {
        imageInput.click();
    });
});

// 处理拖拽悬停
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

// 处理文件拖拽放下
function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (validateFile(file)) {
            uploadAndSearch(file);
        }
    }
}

// 处理文件上传
function handleFileUpload() {
    const file = imageInput.files[0];
    if (file && validateFile(file)) {
        uploadAndSearch(file);
    }
}

// 处理URL搜索
function handleUrlSearch() {
    const url = imageUrl.value.trim();
    if (!url) {
        showError('请输入图片URL');
        return;
    }
    
    if (!isValidUrl(url)) {
        showError('请输入有效的URL地址');
        return;
    }
    
    searchByUrl(url);
}

// 验证文件
function validateFile(file) {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!allowedTypes.includes(file.type)) {
        showError('请选择有效的图片文件 (JPG, PNG, GIF, WEBP)');
        return false;
    }
    
    if (file.size > maxSize) {
        showError('文件大小不能超过 10MB');
        return false;
    }
    
    return true;
}

// 验证URL
function isValidUrl(string) {
    try {
        new URL(string);
        return true;
    } catch (_) {
        return false;
    }
}

// 上传文件并搜索
async function uploadAndSearch(file) {
    // 开始新搜题前的准备工作
    prepareNewSearch();
    showLoading('正在上传图片...');

    const formData = new FormData();
    formData.append('image', file);

    try {
        // 设置30秒超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000);

        showLoading('正在分析题目...');

        const response = await fetch('/api/upload-and-search', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken
            },
            body: formData,
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
            showLoading('正在渲染结果...');
            // 延迟一下让用户看到渲染状态
            setTimeout(() => {
                displayResult(data.data, data.uploaded_image_url);
                hideLoading();
            }, 500);
        } else {
            showError(data.message || '搜题失败，请重试');
        }
    } catch (error) {
        if (error.name === 'AbortError') {
            showError('请求超时，请检查网络连接或稍后重试');
        } else {
            showError('网络错误，请检查网络连接');
        }
        console.error('Upload error:', error);
    } finally {
        hideLoading();
    }
}

// 通过URL搜索
async function searchByUrl(url) {
    // 开始新搜题前的准备工作
    prepareNewSearch();
    showLoading('正在验证图片URL...');

    try {
        // 设置30秒超时
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000);

        showLoading('正在分析题目...');

        const response = await fetch('/api/search-by-url', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': csrfToken
            },
            body: JSON.stringify({ image_url: url }),
            signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success) {
            showLoading('正在渲染结果...');
            // 延迟一下让用户看到渲染状态
            setTimeout(() => {
                displayResult(data.data);
                hideLoading();
            }, 500);
        } else {
            showError(data.message || '搜题失败，请重试');
        }
    } catch (error) {
        if (error.name === 'AbortError') {
            showError('请求超时，请检查网络连接或稍后重试');
        } else {
            showError('网络错误，请检查网络连接');
        }
        console.error('Search error:', error);
    } finally {
        hideLoading();
    }
}

// 显示结果
function displayResult(data, uploadedImageUrl = null) {
    try {
        // 确保数据存在
        if (!data) {
            showError('未收到有效的搜题结果');
            return;
        }

        // 显示上传的图片
        if (uploadedImageUrl) {
            const uploadedImageContainer = document.getElementById('uploadedImageContainer');
            const uploadedImage = document.getElementById('uploadedImage');

            // 添加图片加载错误处理
            uploadedImage.onerror = function() {
                console.warn('上传图片加载失败:', uploadedImageUrl);
                uploadedImageContainer.style.display = 'none';
            };

            uploadedImage.onload = function() {
                uploadedImageContainer.style.display = 'block';
            };

            uploadedImage.src = uploadedImageUrl;
        }

        // 显示题目内容
        const questionText = data.question_text || data.content || '无题目内容';
        document.getElementById('questionText').innerHTML = questionText;

        const questionType = data.question_type || '未知类型';
        document.getElementById('questionType').textContent = questionType;

        // 显示选项
        const optionsContainer = document.getElementById('optionsContainer');
        const optionsList = document.getElementById('optionsList');

        if (data.options && Object.keys(data.options).length > 0) {
            optionsList.innerHTML = '';
            Object.entries(data.options).forEach(([key, value]) => {
                const optionDiv = document.createElement('div');
                optionDiv.className = 'option-item';

                // 安全地处理选项内容
                const safeKey = escapeHtml(key);
                const safeValue = escapeHtml(value);
                optionDiv.innerHTML = `<strong>${safeKey}:</strong> ${safeValue}`;

                optionsList.appendChild(optionDiv);
            });
            optionsContainer.style.display = 'block';
        } else {
            optionsContainer.style.display = 'none';
        }
    
        // 显示答案
        const answerText = data.answer || '暂无答案';
        document.getElementById('answerText').innerHTML = answerText;

        // 显示解析
        const analysisText = data.analysis || '暂无解析';
        document.getElementById('analysisText').innerHTML = analysisText;

        // 显示相关图片
        const relatedImagesContainer = document.getElementById('relatedImagesContainer');
        const relatedImages = document.getElementById('relatedImages');

        if (data.question_img_raw || data.question_img) {
            relatedImages.innerHTML = '';

            if (data.question_img_raw) {
                const img = document.createElement('img');
                img.src = data.question_img_raw;
                img.className = 'image-preview me-2';
                img.alt = '原始处理图片';

                // 添加图片加载错误处理
                img.onerror = function() {
                    console.warn('原始处理图片加载失败:', data.question_img_raw);
                    this.style.display = 'none';
                };

                relatedImages.appendChild(img);
            }

            if (data.question_img && data.question_img !== data.question_img_raw) {
                const img = document.createElement('img');
                img.src = data.question_img;
                img.className = 'image-preview';
                img.alt = '展示图片';

                // 添加图片加载错误处理
                img.onerror = function() {
                    console.warn('展示图片加载失败:', data.question_img);
                    this.style.display = 'none';
                };

                relatedImages.appendChild(img);
            }

            relatedImagesContainer.style.display = 'block';
        } else {
            relatedImagesContainer.style.display = 'none';
        }

        // 显示元信息
        document.getElementById('processTime').textContent = formatProcessTime(data.process_time);
        document.getElementById('cacheHit').textContent = data.cache_hit ? '是' : '否';

        // 显示结果容器
        resultContainer.style.display = 'block';

        // 添加清除按钮
        addClearButton();

        // 滚动到结果区域
        resultContainer.scrollIntoView({ behavior: 'smooth' });

        console.log('搜题结果显示完成:', data);

    } catch (error) {
        console.error('显示结果时出错:', error);
        showError('显示搜题结果时出现错误，请重试');
    }
}

// 准备新搜题 - 清除所有之前的状态和结果
function prepareNewSearch() {
    // 隐藏错误和结果
    hideError();
    hideResult();

    // 清除所有结果内容
    clearAllResults();

    // 重置上传区域状态
    resetUploadArea();
}

// 清除所有结果内容
function clearAllResults() {
    // 清除题目内容
    document.getElementById('questionText').innerHTML = '';
    document.getElementById('questionType').textContent = '';

    // 清除选项
    const optionsList = document.getElementById('optionsList');
    optionsList.innerHTML = '';
    document.getElementById('optionsContainer').style.display = 'none';

    // 清除答案和解析
    document.getElementById('answerText').innerHTML = '';
    document.getElementById('analysisText').innerHTML = '';

    // 清除相关图片
    const relatedImages = document.getElementById('relatedImages');
    relatedImages.innerHTML = '';
    document.getElementById('relatedImagesContainer').style.display = 'none';

    // 清除上传的图片预览
    document.getElementById('uploadedImageContainer').style.display = 'none';
    document.getElementById('uploadedImage').src = '';

    // 重置元信息
    document.getElementById('processTime').textContent = '0';
    document.getElementById('cacheHit').textContent = '否';
}

// 重置上传区域状态
function resetUploadArea() {
    uploadArea.classList.remove('dragover');
    imageInput.value = '';
}

// 显示加载状态
function showLoading(message = '正在分析题目，请稍候...') {
    const loadingText = loading.querySelector('p');
    if (loadingText) {
        loadingText.textContent = message;
    }
    loading.style.display = 'block';

    // 滚动到加载区域
    loading.scrollIntoView({ behavior: 'smooth', block: 'center' });
}

// 隐藏加载状态
function hideLoading() {
    loading.style.display = 'none';
}

// 显示错误
function showError(message) {
    errorMessage.textContent = message;
    errorAlert.style.display = 'block';
    errorAlert.scrollIntoView({ behavior: 'smooth' });

    // 5秒后自动隐藏错误提示
    setTimeout(() => {
        hideError();
    }, 5000);
}

// 隐藏错误
function hideError() {
    errorAlert.style.display = 'none';
}

// 隐藏结果
function hideResult() {
    resultContainer.style.display = 'none';
}

// HTML转义函数，防止XSS攻击
function escapeHtml(text) {
    if (typeof text !== 'string') {
        return text;
    }

    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };

    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// 添加清除按钮功能
function addClearButton() {
    // 检查是否已经有清除按钮
    if (document.getElementById('clearResultsBtn')) {
        return;
    }

    const clearBtn = document.createElement('button');
    clearBtn.id = 'clearResultsBtn';
    clearBtn.className = 'btn btn-clear btn-sm mt-3';
    clearBtn.innerHTML = '<i class="fas fa-trash"></i> 清除结果';
    clearBtn.onclick = function() {
        prepareNewSearch();
        clearBtn.remove();
    };

    // 将按钮添加到结果容器的底部
    resultContainer.appendChild(clearBtn);
}

// 格式化处理时间显示
function formatProcessTime(time) {
    if (!time || time === 0) {
        return '0';
    }

    if (time < 1000) {
        return time + '';
    } else {
        return (time / 1000).toFixed(2) + 's';
    }
}
