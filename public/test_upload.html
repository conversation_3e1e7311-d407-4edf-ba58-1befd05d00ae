<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试页面</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .result { background: #e8f5e9; padding: 15px; margin: 10px 0; border-radius: 5px; white-space: pre-wrap; }
        .error { background: #ffebee; color: #c62828; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        input[type="file"], input[type="url"] { width: 100%; padding: 8px; margin: 10px 0; }
        .log-container { background: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 20px 0; border-radius: 5px; max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 拍照搜题API测试工具</h1>
        
        <div class="test-section">
            <h2>📤 文件上传测试</h2>
            <input type="file" id="fileInput" accept="image/*">
            <button onclick="testFileUpload()">测试文件上传</button>
            <div id="uploadResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h2>🔗 URL搜题测试</h2>
            <input type="url" id="urlInput" placeholder="输入图片URL" value="https://example.com/test.jpg">
            <button onclick="testUrlSearch()">测试URL搜题</button>
            <div id="urlResult" class="result" style="display:none;"></div>
        </div>
        
        <div class="test-section">
            <h2>📋 实时日志</h2>
            <button onclick="fetchLogs()">刷新日志</button>
            <button onclick="clearLogs()">清空日志</button>
            <div id="logContainer" class="log-container">点击"刷新日志"查看最新日志...</div>
        </div>
    </div>

    <script>
        // 获取CSRF Token
        async function getCSRFToken() {
            try {
                const response = await fetch('/');
                const html = await response.text();
                const match = html.match(/name="csrf-token" content="([^"]+)"/);
                return match ? match[1] : null;
            } catch (error) {
                console.error('获取CSRF Token失败:', error);
                return null;
            }
        }

        // 测试文件上传
        async function testFileUpload() {
            const fileInput = document.getElementById('fileInput');
            const resultDiv = document.getElementById('uploadResult');
            
            if (!fileInput.files[0]) {
                alert('请选择一个文件');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在上传和搜题...';
            resultDiv.className = 'result';
            
            try {
                const token = await getCSRFToken();
                if (!token) {
                    throw new Error('无法获取CSRF Token');
                }
                
                const formData = new FormData();
                formData.append('image', fileInput.files[0]);
                
                const response = await fetch('/api/upload-and-search', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': token
                    },
                    body: formData
                });
                
                const result = await response.text();
                
                try {
                    const jsonResult = JSON.parse(result);
                    resultDiv.innerHTML = `✅ 上传成功！\n\n${JSON.stringify(jsonResult, null, 2)}`;
                } catch {
                    resultDiv.innerHTML = `📄 响应内容:\n${result}`;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.innerHTML = `❌ 错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
            
            // 自动刷新日志
            setTimeout(fetchLogs, 1000);
        }

        // 测试URL搜题
        async function testUrlSearch() {
            const urlInput = document.getElementById('urlInput');
            const resultDiv = document.getElementById('urlResult');
            
            if (!urlInput.value) {
                alert('请输入图片URL');
                return;
            }
            
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '🔄 正在搜题...';
            resultDiv.className = 'result';
            
            try {
                const token = await getCSRFToken();
                if (!token) {
                    throw new Error('无法获取CSRF Token');
                }
                
                const response = await fetch('/api/search-by-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': token
                    },
                    body: JSON.stringify({
                        image_url: urlInput.value
                    })
                });
                
                const result = await response.text();
                
                try {
                    const jsonResult = JSON.parse(result);
                    resultDiv.innerHTML = `✅ 搜题成功！\n\n${JSON.stringify(jsonResult, null, 2)}`;
                } catch {
                    resultDiv.innerHTML = `📄 响应内容:\n${result}`;
                    resultDiv.className = 'result error';
                }
                
            } catch (error) {
                resultDiv.innerHTML = `❌ 错误: ${error.message}`;
                resultDiv.className = 'result error';
            }
            
            // 自动刷新日志
            setTimeout(fetchLogs, 1000);
        }

        // 获取日志
        async function fetchLogs() {
            const logContainer = document.getElementById('logContainer');
            
            try {
                const response = await fetch('/api/get-logs');
                const logs = await response.text();
                logContainer.innerHTML = logs || '暂无日志';
            } catch (error) {
                logContainer.innerHTML = `获取日志失败: ${error.message}`;
            }
        }

        // 清空日志
        async function clearLogs() {
            try {
                await fetch('/api/clear-logs', { method: 'POST' });
                document.getElementById('logContainer').innerHTML = '日志已清空';
            } catch (error) {
                console.error('清空日志失败:', error);
            }
        }

        // 页面加载时自动获取日志
        window.onload = function() {
            fetchLogs();
        };
    </script>
</body>
</html>
