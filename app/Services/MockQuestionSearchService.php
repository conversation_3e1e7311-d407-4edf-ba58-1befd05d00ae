<?php

namespace App\Services;

// 移除Log依赖，避免在非Web环境下出错

class MockQuestionSearchService
{
    /**
     * 模拟搜索题目
     * 用于演示系统功能，当没有真实API时使用
     */
    public function searchQuestion(string $imageUrl): ?array
    {
        // 记录模拟搜索开始
        error_log("=== Mock Question Search Started ===");
        error_log("Image URL: $imageUrl");

        // 模拟API处理时间
        usleep(500000); // 0.5秒

        // 生成符合真实API格式的模拟数据
        $mockData = [
            'code' => 200,
            'message' => '搜题成功',
            'data' => [
                'id' => rand(100, 999),
                'content' => '驾驶机动车在高速公路发生故障,需要停车排除故障时,以下做法先后顺序正确的是？①放置警告标志,转移乘车人员至安全处,迅速报警②开启危险报警闪光灯③将车辆移至不妨碍交通的位置④等待救援',
                'question_type' => '单选题',
                'question_text' => '驾驶机动车在高速公路发生故障,需要停车排除故障时,以下做法先后顺序正确的是？①放置警告标志,转移乘车人员至安全处,迅速报警②开启危险报警闪光灯③将车辆移至不妨碍交通的位置④等待救援',
                'options' => [
                    'A' => '③②①④',
                    'B' => '①②③④',
                    'C' => '②③①④',
                    'D' => '④③①②'
                ],
                'analysis' => '在高速公路发生故障时，正确的操作顺序应该是：首先开启危险报警闪光灯（②），以警示其他车辆；其次，尽可能将车辆移至不妨碍交通的位置（③），以减少对其他车辆的影响；然后放置警告标志，转移乘车人员至安全处，迅速报警（①），确保人员安全和及时获得帮助；最后等待救援（④）。因此，正确的顺序是②③①④，对应选项C。',
                'answer' => 'C:②③①④',
                'cache_hit' => false,
                'process_time' => rand(15000, 25000),
                'question_img_raw' => $imageUrl
            ]
        ];

        // 记录生成的模拟数据
        error_log("Mock data generated: " . json_encode($mockData, JSON_UNESCAPED_UNICODE));
        error_log("=== Mock Question Search Completed ===");

        // 返回data部分，与真实API保持一致
        return $mockData['data'];
    }

    /**
     * 格式化搜题结果
     */
    public function formatResult(array $data): array
    {
        return [
            'id' => $data['id'] ?? null,
            'question_text' => $data['question_text'] ?? $data['content'] ?? '',
            'question_type' => $data['question_type'] ?? '',
            'options' => $data['options'] ?? [],
            'answer' => $data['answer'] ?? '',
            'analysis' => $data['analysis'] ?? '',
            'cache_hit' => $data['cache_hit'] ?? false,
            'process_time' => $data['process_time'] ?? 0,
            'question_img_raw' => $data['question_img_raw'] ?? null,
            'question_img' => $data['question_img'] ?? null,
        ];
    }

    /**
     * 验证图片URL格式
     */
    public function validateImageUrl(string $url): bool
    {
        $pattern = '/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i';
        return preg_match($pattern, $url) === 1;
    }

    /**
     * 获取错误信息
     */
    public function getErrorMessage(int $code): string
    {
        $messages = [
            400 => '请求参数错误，请检查图片URL格式',
            401 => 'API密钥无效，请检查配置',
            402 => '余额不足，请充值后重试',
            403 => '账户已被冻结，请联系客服',
            429 => '请求过于频繁，请稍后重试',
            500 => '服务器繁忙，请稍后重试',
        ];

        return $messages[$code] ?? '未知错误，请联系技术支持';
    }
}
