<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

class FileValidationService
{
    /**
     * 验证上传的文件是否为有效图片
     * 使用多重验证策略，更加宽松但仍然安全
     */
    public function validateImageFile(UploadedFile $file): array
    {
        $errors = [];
        $warnings = [];

        // 1. 基础检查：文件扩展名
        $allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp'];
        $extension = strtolower($file->getClientOriginalExtension());

        if (!in_array($extension, $allowedExtensions)) {
            $errors[] = "不支持的文件扩展名: $extension";
        }

        // 2. 基础检查：文件大小
        $maxSize = 10 * 1024 * 1024; // 10MB
        if ($file->getSize() > $maxSize) {
            $errors[] = '文件大小超过10MB限制';
        }

        if ($file->getSize() < 100) { // 小于100字节可能不是有效图片
            $errors[] = '文件太小，可能不是有效图片';
        }

        // 3. 优先使用fileinfo验证（如果可用）
        $fileinfoValid = $this->validateWithFileinfo($file);

        // 4. 使用GD库验证
        $gdValid = $this->validateWithGD($file);

        // 5. 文件头签名验证
        $signatureValid = $this->validateFileSignature($file);

        // 验证策略：至少有一种方法验证成功
        $validationMethods = [
            'fileinfo' => $fileinfoValid,
            'gd' => $gdValid,
            'signature' => $signatureValid
        ];

        $validCount = count(array_filter($validationMethods));

        if ($validCount === 0) {
            $errors[] = '无法验证文件为有效图片格式';
        } elseif ($validCount === 1) {
            $warnings[] = '图片格式验证通过，但建议检查文件完整性';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'validation_methods' => $validationMethods
        ];
    }

    /**
     * 使用fileinfo扩展验证
     */
    private function validateWithFileinfo(UploadedFile $file): bool
    {
        if (!extension_loaded('fileinfo')) {
            return false; // 扩展不可用，不算验证失败
        }

        try {
            $finfo = finfo_open(FILEINFO_MIME_TYPE);
            if (!$finfo) {
                return false;
            }

            $mimeType = finfo_file($finfo, $file->getRealPath());
            finfo_close($finfo);

            $allowedMimes = [
                'image/jpeg',
                'image/png',
                'image/gif',
                'image/webp',
                'image/bmp',
                'image/x-ms-bmp'
            ];

            return in_array($mimeType, $allowedMimes);
        } catch (\Exception $e) {
            Log::warning('Fileinfo validation failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 通过文件头部签名验证文件类型
     */
    private function validateFileSignature(UploadedFile $file): bool
    {
        $handle = fopen($file->getRealPath(), 'rb');
        if (!$handle) {
            return false;
        }
        
        $header = fread($handle, 12);
        fclose($handle);
        
        // 检查常见图片格式的文件头
        $signatures = [
            'jpeg' => [
                "\xFF\xD8\xFF\xE0",  // JPEG JFIF
                "\xFF\xD8\xFF\xE1",  // JPEG EXIF
                "\xFF\xD8\xFF\xDB",  // JPEG
            ],
            'png' => [
                "\x89\x50\x4E\x47\x0D\x0A\x1A\x0A", // PNG
            ],
            'gif' => [
                "GIF87a",  // GIF87a
                "GIF89a",  // GIF89a
            ],
            'webp' => [
                "RIFF", // WebP (需要进一步检查)
            ]
        ];
        
        foreach ($signatures as $type => $sigs) {
            foreach ($sigs as $sig) {
                if (substr($header, 0, strlen($sig)) === $sig) {
                    // WebP需要额外检查
                    if ($type === 'webp') {
                        return substr($header, 8, 4) === 'WEBP';
                    }
                    return true;
                }
            }
        }
        
        return false;
    }
    
    /**
     * 使用GD库验证图片
     */
    private function validateWithGD(UploadedFile $file): bool
    {
        if (!extension_loaded('gd')) {
            return false; // GD不可用，返回false但不算致命错误
        }

        try {
            $imageInfo = getimagesize($file->getRealPath());

            if ($imageInfo === false) {
                return false;
            }

            // 检查是否为支持的图片类型
            $allowedTypes = [
                IMAGETYPE_JPEG,
                IMAGETYPE_PNG,
                IMAGETYPE_GIF,
                IMAGETYPE_WEBP,
                IMAGETYPE_BMP
            ];

            $isValidType = in_array($imageInfo[2], $allowedTypes);

            // 额外检查：图片尺寸是否合理
            if ($isValidType) {
                $width = $imageInfo[0];
                $height = $imageInfo[1];

                // 检查尺寸是否合理（1x1到10000x10000）
                if ($width < 1 || $height < 1 || $width > 10000 || $height > 10000) {
                    Log::warning("Image dimensions seem unreasonable: {$width}x{$height}");
                    return false;
                }
            }

            return $isValidType;

        } catch (\Exception $e) {
            Log::warning('GD image validation failed: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 检查文件是否可能包含恶意代码
     */
    public function scanForMaliciousContent(UploadedFile $file): bool
    {
        $content = file_get_contents($file->getRealPath());
        
        // 检查常见的恶意代码模式
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/eval\s*\(/i',
            '/exec\s*\(/i',
            '/system\s*\(/i',
            '/shell_exec/i',
            '/base64_decode/i',
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                Log::warning('Malicious content detected in uploaded file', [
                    'pattern' => $pattern,
                    'filename' => $file->getClientOriginalName()
                ]);
                return true;
            }
        }
        
        return false;
    }
}
