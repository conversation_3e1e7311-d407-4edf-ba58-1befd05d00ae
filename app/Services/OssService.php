<?php

namespace App\Services;

use OSS\OssClient;
use OSS\Core\OssException;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Log;

class OssService
{
    private $ossClient;
    private $bucket;
    private $domain;

    public function __construct()
    {
        $accessKeyId = config('services.oss.access_key_id');
        $accessKeySecret = config('services.oss.access_key_secret');
        $endpoint = config('services.oss.endpoint');
        $this->bucket = config('services.oss.bucket');
        $this->domain = config('services.oss.domain');

        try {
            $this->ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);
        } catch (OssException $e) {
            Log::error('OSS Client initialization failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 上传文件到OSS
     *
     * @param UploadedFile $file
     * @param string $directory
     * @return string|false
     */
    public function uploadFile(UploadedFile $file, string $directory = 'images')
    {
        try {
            // 生成唯一文件名
            $extension = $file->getClientOriginalExtension();
            $filename = $directory . '/' . date('Y/m/d') . '/' . uniqid() . '.' . $extension;
            
            // 获取文件内容
            $content = file_get_contents($file->getRealPath());
            
            // 上传到OSS
            $result = $this->ossClient->putObject($this->bucket, $filename, $content);
            
            if ($result) {
                // 返回完整的URL
                return 'https://' . $this->domain . '/' . $filename;
            }
            
            return false;
        } catch (OssException $e) {
            Log::error('OSS upload failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 删除OSS文件
     *
     * @param string $url
     * @return bool
     */
    public function deleteFile(string $url): bool
    {
        try {
            // 从URL中提取文件路径
            $path = str_replace('https://' . $this->domain . '/', '', $url);
            
            $this->ossClient->deleteObject($this->bucket, $path);
            return true;
        } catch (OssException $e) {
            Log::error('OSS delete failed: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param string $url
     * @return bool
     */
    public function fileExists(string $url): bool
    {
        try {
            $path = str_replace('https://' . $this->domain . '/', '', $url);
            return $this->ossClient->doesObjectExist($this->bucket, $path);
        } catch (OssException $e) {
            Log::error('OSS file exists check failed: ' . $e->getMessage());
            return false;
        }
    }
}
