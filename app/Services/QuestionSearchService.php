<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class QuestionSearchService
{
    private $apiUrl;
    private $appKey;
    private $secretKey;

    public function __construct()
    {
        $this->apiUrl = config('services.question_api.url');
        $this->appKey = config('services.question_api.key');
        $this->secretKey = config('services.question_api.secret');
    }

    /**
     * 搜索题目
     *
     * @param string $imageUrl
     * @return array|null
     */
    public function searchQuestion(string $imageUrl): ?array
    {
        // 记录发送给API的原始数据
        $requestData = [
            'image_url' => $imageUrl
        ];

        $requestHeaders = [
            'Content-Type' => 'application/json',
            'X-App-Key' => $this->appKey,
            'X-Secret-Key' => $this->secretKey,
        ];

        Log::info('=== API请求开始 ===');
        Log::info('API URL: ' . $this->apiUrl);
        Log::info('请求头原始数据: ' . json_encode($requestHeaders, JSON_UNESCAPED_UNICODE));
        Log::info('请求体原始数据: ' . json_encode($requestData, JSON_UNESCAPED_UNICODE));

        try {
            $response = Http::timeout(30)
                ->withHeaders($requestHeaders)
                ->post($this->apiUrl, $requestData);

            // 记录HTTP响应的原始信息
            Log::info('=== API响应信息 ===');
            Log::info('HTTP状态码: ' . $response->status());
            Log::info('响应头原始数据: ' . json_encode($response->headers(), JSON_UNESCAPED_UNICODE));
            Log::info('响应体原始数据(未处理): ' . $response->body());

            if ($response->successful()) {
                $data = $response->json();
                Log::info('响应体JSON解析后: ' . json_encode($data, JSON_UNESCAPED_UNICODE));

                if (isset($data['code']) && $data['code'] === 200) {
                    Log::info('API调用成功，返回data字段: ' . json_encode($data['data'], JSON_UNESCAPED_UNICODE));
                    return $data['data'];
                } else {
                    Log::error('API返回错误码: ' . ($data['code'] ?? 'unknown'));
                    Log::error('API错误信息: ' . ($data['message'] ?? 'no message'));
                    Log::error('完整错误响应: ' . json_encode($data, JSON_UNESCAPED_UNICODE));
                    return null;
                }
            } else {
                Log::error('HTTP请求失败');
                Log::error('HTTP状态码: ' . $response->status());
                Log::error('HTTP状态文本: ' . $response->reason());
                Log::error('响应体原始内容: ' . $response->body());
                return null;
            }
        } catch (\Exception $e) {
            Log::error('=== API请求异常 ===');
            Log::error('异常类型: ' . get_class($e));
            Log::error('异常消息: ' . $e->getMessage());
            Log::error('异常文件: ' . $e->getFile());
            Log::error('异常行号: ' . $e->getLine());
            Log::error('异常堆栈: ' . $e->getTraceAsString());
            return null;
        } finally {
            Log::info('=== API请求结束 ===');
        }
    }

    /**
     * 格式化搜题结果
     *
     * @param array $data
     * @return array
     */
    public function formatResult(array $data): array
    {
        return [
            'id' => $data['id'] ?? null,
            'question_text' => $data['question_text'] ?? $data['content'] ?? '',
            'question_type' => $data['question_type'] ?? '',
            'options' => $data['options'] ?? [],
            'answer' => $data['answer'] ?? '',
            'analysis' => $data['analysis'] ?? '',
            'cache_hit' => $data['cache_hit'] ?? false,
            'process_time' => $data['process_time'] ?? 0,
            'question_img_raw' => $data['question_img_raw'] ?? null,
            'question_img' => $data['question_img'] ?? null,
        ];
    }

    /**
     * 验证图片URL格式
     *
     * @param string $url
     * @return bool
     */
    public function validateImageUrl(string $url): bool
    {
        $pattern = '/^https?:\/\/.+\.(jpg|jpeg|png|gif|webp)$/i';
        return preg_match($pattern, $url) === 1;
    }

    /**
     * 获取错误信息
     *
     * @param int $code
     * @return string
     */
    public function getErrorMessage(int $code): string
    {
        $messages = [
            400 => '请求参数错误，请检查图片URL格式',
            401 => 'API密钥无效，请检查配置',
            402 => '余额不足，请充值后重试',
            403 => '账户已被冻结，请联系客服',
            429 => '请求过于频繁，请稍后重试',
            500 => '服务器繁忙，请稍后重试',
        ];

        return $messages[$code] ?? '未知错误，请联系技术支持';
    }
}
