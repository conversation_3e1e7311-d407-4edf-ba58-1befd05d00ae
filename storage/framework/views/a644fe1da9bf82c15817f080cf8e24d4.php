<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title>拍照搜题系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        .main-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 50px auto;
            max-width: 800px;
            overflow: hidden;
        }
        .header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            margin: 30px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #764ba2;
            background-color: #f8f9ff;
        }
        .upload-area.dragover {
            border-color: #28a745;
            background-color: #e8f5e8;
        }
        .btn-upload {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 25px;
            padding: 12px 30px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-upload:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .result-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 25px;
            margin: 20px;
            display: none;
        }
        .question-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .option-item {
            background: #e9ecef;
            border-radius: 8px;
            padding: 10px 15px;
            margin: 5px 0;
            transition: all 0.3s ease;
        }
        .option-item:hover {
            background: #dee2e6;
        }
        .answer-highlight {
            background: #d4edda !important;
            border-left: 4px solid #28a745;
        }
        .loading {
            display: none;
            text-align: center;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            margin: 20px 30px;
            border: 2px solid #e9ecef;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .loading p {
            color: #667eea;
            font-weight: 500;
            margin: 0;
            font-size: 16px;
        }
        .loading .loading-dots {
            display: inline-block;
            animation: dots 1.5s infinite;
        }
        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .url-input-group {
            margin: 20px 30px;
        }
        .alert {
            margin: 20px 30px;
            border-radius: 10px;
            animation: slideIn 0.3s ease-out;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .result-container {
            animation: fadeIn 0.5s ease-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .btn-clear {
            background: #6c757d;
            border: none;
            color: white;
            transition: all 0.3s ease;
        }
        .btn-clear:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }
        .progress-indicator {
            display: none;
            margin: 10px 0;
        }
        .progress-bar {
            background: linear-gradient(45deg, #667eea, #764ba2);
            animation: progress 2s ease-in-out infinite;
        }
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-container">
            <!-- 头部 -->
            <div class="header">
                <h1><i class="fas fa-camera"></i> 拍照搜题系统</h1>
                <p class="mb-0">上传题目图片，快速获取答案和解析</p>
            </div>

            <!-- 上传区域 -->
            <div class="upload-area" id="uploadArea">
                <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                <h4>点击或拖拽上传图片</h4>
                <p class="text-muted">支持 JPG、PNG、GIF、WEBP 格式，最大 10MB</p>
                <input type="file" id="imageInput" accept="image/*" style="display: none;">
                <button type="button" class="btn btn-upload" id="selectImageBtn">
                    <i class="fas fa-upload"></i> 选择图片
                </button>
            </div>

            <!-- URL输入区域 -->
            <div class="url-input-group">
                <div class="input-group">
                    <input type="url" class="form-control" id="imageUrl" placeholder="或者输入图片URL地址">
                    <button class="btn btn-upload" type="button" id="searchByUrl">
                        <i class="fas fa-search"></i> 搜索
                    </button>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>正在分析题目，请稍候<span class="loading-dots"></span></p>
                <small class="text-muted">请耐心等待，API响应可能需要一些时间</small>
            </div>

            <!-- 错误提示 -->
            <div id="errorAlert" class="alert alert-danger" style="display: none;">
                <i class="fas fa-exclamation-triangle"></i>
                <span id="errorMessage"></span>
            </div>

            <!-- 结果显示区域 -->
            <div class="result-container" id="resultContainer">
                <h3><i class="fas fa-lightbulb"></i> 搜题结果</h3>
                
                <!-- 上传的图片预览 -->
                <div id="uploadedImageContainer" style="display: none;">
                    <h5>上传的图片：</h5>
                    <img id="uploadedImage" class="image-preview" alt="上传的图片">
                </div>

                <!-- 题目内容 -->
                <div class="question-card">
                    <h5><i class="fas fa-question-circle"></i> 题目</h5>
                    <div id="questionText"></div>
                    <div id="questionType" class="badge bg-primary mt-2"></div>
                </div>

                <!-- 选项 -->
                <div class="question-card" id="optionsContainer" style="display: none;">
                    <h5><i class="fas fa-list"></i> 选项</h5>
                    <div id="optionsList"></div>
                </div>

                <!-- 答案 -->
                <div class="question-card">
                    <h5><i class="fas fa-check-circle"></i> 答案</h5>
                    <div id="answerText" class="answer-highlight p-3 rounded"></div>
                </div>

                <!-- 解析 -->
                <div class="question-card">
                    <h5><i class="fas fa-book-open"></i> 解析</h5>
                    <div id="analysisText"></div>
                </div>

                <!-- 相关图片 -->
                <div id="relatedImagesContainer" style="display: none;">
                    <div class="question-card">
                        <h5><i class="fas fa-images"></i> 相关图片</h5>
                        <div id="relatedImages"></div>
                    </div>
                </div>

                <!-- 元信息 -->
                <div class="question-card">
                    <h5><i class="fas fa-info-circle"></i> 其他信息</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-clock"></i> 处理时间: <span id="processTime"></span>ms
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <i class="fas fa-database"></i> 缓存命中: <span id="cacheHit"></span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="<?php echo e(asset('js/app.js')); ?>"></script>
</body>
</html>
<?php /**PATH /Users/<USER>/Herd/paizhao_test/resources/views/question/index.blade.php ENDPATH**/ ?>